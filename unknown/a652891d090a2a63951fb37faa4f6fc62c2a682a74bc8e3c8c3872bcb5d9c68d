# 简化版小区选择功能使用指南

## 🎯 核心特性

已实现简化的全局小区选择功能，采用类似微信小程序的存储模式：

### ✅ 简单的API调用
```javascript
import { getSelectedCommunityId } from '@/store/modules/options'

// 获取当前选中的小区ID - 只需一行代码！
const communityId = getSelectedCommunityId()
```

### ✅ 自动监听小区变化
```javascript
export default {
  methods: {
    // 定义此方法，小区变化时自动调用
    onCommunityChange(community) {
      this.dataList = []  // 清空数据
      if (community) {
        this.loadData()   // 重新加载
      }
    }
  }
}
```

## 📚 API参考

### 核心方法

| 方法名 | 返回值 | 说明 |
|--------|--------|------|
| `getSelectedCommunityId()` | `number\|null` | 获取当前选中的小区ID |
| `getSelectedCommunity()` | `Object\|null` | 获取当前选中的小区对象 |
| `hasSelectedCommunity()` | `boolean` | 检查是否有选中的小区 |
| `setSelectedCommunity(community)` | `void` | 设置选中的小区 |
| `setCommunityList(list)` | `void` | 设置小区列表 |

### 监听方法

| 方法名 | 说明 |
|--------|------|
| `addCommunityChangeListener(listener)` | 添加小区变化监听器 |
| `removeCommunityChangeListener(listener)` | 移除小区变化监听器 |

## 🚀 使用示例

### 示例1: 基本使用（推荐）

```javascript
import { getSelectedCommunityId, getSelectedCommunity } from '@/store/modules/options'

export default {
  data() {
    return {
      dataList: [],
      total: 0
    }
  },
  
  methods: {
    // 加载数据
    loadData() {
      // 获取小区ID - 简单直接！
      const communityId = getSelectedCommunityId()
      
      if (!communityId) {
        this.$message.warning('请先选择小区')
        return
      }
      
      // 调用API
      this.fetchDataAPI({ 
        ...this.searchParams, 
        communityId 
      }).then(res => {
        this.dataList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    
    // 小区变化时自动调用（通过mixin自动注册）
    onCommunityChange(community) {
      this.dataList = []
      this.total = 0
      
      if (community) {
        this.loadData()
        this.$message.success(`已切换到：${community.communityName}`)
      }
    }
  },
  
  created() {
    // 初始化时检查是否有选中的小区
    if (getSelectedCommunity()) {
      this.loadData()
    }
  }
}
```

### 示例2: 在计算属性中使用

```javascript
import { getSelectedCommunity, hasSelectedCommunity } from '@/store/modules/options'

export default {
  computed: {
    currentCommunity() {
      return getSelectedCommunity()
    },
    
    hasCurrentCommunity() {
      return hasSelectedCommunity()
    },
    
    communityName() {
      return this.currentCommunity?.communityName || '未选择'
    }
  }
}
```

### 示例3: 在模板中使用

```vue
<template>
  <div>
    <!-- 显示当前小区信息 -->
    <div v-if="currentCommunity" class="community-info">
      当前小区：{{ currentCommunity.communityName }}
    </div>
    
    <!-- 未选择小区时的提示 -->
    <div v-else class="warning">
      请先在顶部导航栏选择小区
    </div>
    
    <!-- 按钮状态控制 -->
    <el-button 
      @click="loadData" 
      :disabled="!hasCurrentCommunity"
    >
      查询数据
    </el-button>
  </div>
</template>
```

## 🔧 技术实现

### 存储机制
- 使用localStorage进行数据持久化
- 页面刷新后保持选择状态
- 用户退出登录时自动清空

### 监听机制
- 通过全局mixin自动为所有组件添加监听能力
- 组件只需定义`onCommunityChange`方法即可
- 无需手动注册/注销事件监听器

### 数据流
1. 用户在顶部导航栏选择小区
2. 调用`setSelectedCommunity()`保存到全局状态和localStorage
3. 触发所有页面的`onCommunityChange`方法
4. 各页面清空数据并重新加载

## 📋 已集成的页面

- ✅ 小区住户管理页面 (`communityResidentList.vue`)
- ✅ 小区楼栋管理页面 (`communityBuildingList.vue`)
- ✅ 测试页面 (`test/CommunityTest.vue`)

## 🎉 优势对比

### 之前的复杂方式
```javascript
// ❌ 复杂的调用方式
this.$options.get('selectCommunity')?.id

// ❌ 需要复杂的mixin配置
// ❌ 容易出现 "is not a function" 错误
```

### 现在的简化方式
```javascript
// ✅ 简单直接的调用方式
getSelectedCommunityId()

// ✅ 类似微信小程序的存储模式
// ✅ 稳定可靠，不会出现函数调用错误
```

## 🚨 注意事项

1. **导入方式**: 必须从`@/store/modules/options`导入方法
2. **空值检查**: 使用前检查返回值是否为null
3. **监听方法**: 组件中定义`onCommunityChange`方法即可自动监听
4. **生命周期**: mixin会自动处理监听器的注册和注销

## 🔄 迁移指南

如果您之前使用了复杂的`this.$options.get()`方式，请按以下步骤迁移：

1. **添加导入**:
```javascript
import { getSelectedCommunityId, getSelectedCommunity } from '@/store/modules/options'
```

2. **替换调用方式**:
```javascript
// 旧方式
const communityId = this.$options.get('selectCommunity')?.id

// 新方式
const communityId = getSelectedCommunityId()
```

3. **保持监听方法不变**:
```javascript
// 这个方法保持不变，会自动工作
onCommunityChange(community) {
  // 处理小区变化
}
```

现在您可以享受简单、稳定的全局小区选择功能了！🎊
