<!-- 商品列表页面
•	搜索区域：
o	商品名称（输入框）
o	商品分类（下拉选择：全部、积分商品、实物商品等）
o	商品状态（下拉选择：全部、上架、下架）
o	搜索按钮
•	操作按钮：
o	新增商品
o	批量上架/下架

•	列表展示：
o	序号
o	商品图片
o	商品名称
o	商品分类
o	商品价格/积分
o	库存数量
o	销售数量
o	商品状态
o	创建时间
o	操作（编辑、删除、上架/下架）

•	分页控件

新增/编辑商品弹窗
•	商品名称（输入框，*必填）
•	商品分类（下拉选择，*必填）
•	商品图片（多图片上传，*必填）
•	商品价格类型（单选按钮：积分/现金/积分+现金）
•	积分价格（数字输入框，根据价格类型必填）
•	现金价格（数字输入框，根据价格类型必填）
•	商品库存（数字输入框，*必填）
•	商品简介（文本域）
•	商品详情（富文本编辑器，*必填）
•	商品状态（单选按钮：上架/下架）
•	确定/取消按钮 -->


<!-- 1.新增通知时,初始化弹窗页面.
2.在目标ids的右侧显示一个选择按钮
3.目标类型是楼栋时,点击选择按钮, 根据当前全局保存的小区id查询楼栋列表,弹出楼栋列表,选择楼栋,多选,所选id保存到ids参数里
4.目标类型是用户时,点击选择按钮,弹出树形结构,根据当前全局保存的小区id查询楼栋列表,接口地址在communityBuilding.js里.
比如:
/manage-api/v1/community/building/page?pageNum=1&pageSize=500&buildingNumber=&type=&communityId=2,
点击楼栋时,查询所点击楼栋的房间列表,多选房间,所选id保存到ids参数里,
点击不同的楼栋时,查询所点击楼栋的房间列表,根据ids是否存在已选择该楼栋下的房间,自动勾选显示是否已选择该房间.
目标类型是用户时,只保存房间id至ids里,不保存楼栋id -->


<!-- 1.目标ids的input是不可编辑的.
2.目标类型是全体时,清空ids的input显示,并清空ids参数,
3.目标类型是楼栋时,显示方式也按照目标类型是用户时的显示效果来展示,只不过楼栋前面的框是可选的.
4.虽然我们保存的是ids,但可以让目标ids的input框显示为楼栋名称,这样用户知道已经选择了哪些楼栋,同理,选择房间时,目标ids的input框显示为房间名称的逗号分隔形式.
5.目标ids的input框的宽度,可以根据内容来动态改变,充满宽度后可以换行显示. -->

列表置顶显示根据'top':true字段显示 是 或者 否 


