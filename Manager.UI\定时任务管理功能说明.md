# 定时任务管理功能说明

## 📋 功能概述

定时任务管理系统提供了完整的定时任务增删改查功能，支持任务的启动、停止、立即执行等操作。

## 🗂️ 文件结构

```
Manager.UI/
├── src/
│   ├── api/job/
│   │   └── index.js                 # 定时任务API接口
│   ├── views/job/
│   │   ├── list.vue                 # 定时任务列表页面
│   │   └── components/
│   │       ├── JobEdit.vue          # 任务编辑弹窗
│   │       └── JobDetail.vue        # 任务详情弹窗
│   └── router/index.js              # 路由配置（已添加定时任务路由）
```

## 🚀 功能特性

### 📊 任务列表页面
- **搜索功能**：支持按任务名称、任务组、状态搜索
- **分页显示**：支持分页查看任务列表
- **状态管理**：实时显示任务运行状态
- **操作按钮**：启动、停止、执行、编辑、删除、详情查看

### ✏️ 任务编辑功能
- **基本信息**：任务名称、任务分组、调用目标
- **执行配置**：Cron表达式、错失执行策略、并发控制
- **日志管理**：日志保留天数设置
- **状态控制**：任务启用/禁用状态

### 📋 任务详情查看
- **完整信息展示**：所有任务配置信息
- **创建/更新信息**：创建人、创建时间、更新时间
- **格式化显示**：代码块格式显示调用目标和Cron表达式

## 🔧 API接口

### 基础CRUD操作
- `GET /manage-api/v1/job/page` - 分页查询任务列表
- `GET /manage-api/v1/job` - 获取任务详情
- `POST /manage-api/v1/job` - 新增任务
- `PUT /manage-api/v1/job` - 编辑任务
- `DELETE /manage-api/v1/job` - 删除任务

### 任务控制操作
- `POST /manage-api/v1/job/start/{id}` - 启动任务
- `POST /manage-api/v1/job/stop/{id}` - 停止任务
- `POST /manage-api/v1/job/run/{id}` - 立即执行任务

## 📝 数据字典

### 任务状态 (job_status)
- `run` - 运行中
- `stop` - 已停止

### 任务组 (jobGroups)
- `default` - 默认组
- `system` - 系统组
- `business` - 业务组

### 错失执行策略
- `default` - 默认策略
- `ignore` - 忽略错失
- `fire_once` - 立即执行一次
- `do_nothing` - 不执行

### 并发执行
- `true` - 允许
- `false` - 禁止

## 🎯 使用说明

### 1. 访问页面
通过路由 `/job/list` 访问定时任务管理页面

### 2. 新增任务
1. 点击"新增任务"按钮
2. 填写任务基本信息
3. 配置Cron表达式（可点击"生成表达式"查看帮助）
4. 设置错失执行策略和并发控制
5. 保存任务

### 3. 管理任务
- **启动任务**：点击"启动"按钮启动已停止的任务
- **停止任务**：点击"停止"按钮停止正在运行的任务
- **立即执行**：点击"执行"按钮立即执行一次任务
- **编辑任务**：点击"编辑"按钮修改任务配置
- **查看详情**：点击"详情"按钮查看任务完整信息
- **删除任务**：点击"删除"按钮删除任务

### 4. Cron表达式示例
- `0 0 2 1 * ?` - 每月1日凌晨2点执行
- `0 15 10 ? * MON-FRI` - 周一到周五上午10:15执行
- `0 0 12 * * ?` - 每天中午12点执行
- `0 0/30 9-17 * * ?` - 工作时间内每半小时执行一次

## 🔍 技术特点

### 字典驱动
- 任务状态和任务组通过字典配置，支持动态更新
- 降级机制：字典加载失败时使用默认配置

### 响应式设计
- 支持PC端和移动端访问
- 表格列宽自适应
- 操作按钮响应式布局

### 用户体验
- 操作确认提示
- 加载状态显示
- 错误信息友好提示
- Cron表达式帮助文档

### 代码规范
- 组件化设计
- API统一管理
- 错误处理完善
- 注释文档齐全

## 🚨 注意事项

1. **Cron表达式格式**：使用标准的6位或7位Cron表达式
2. **任务调用目标**：确保调用的Bean或Class存在且可访问
3. **并发控制**：根据任务特性选择是否允许并发执行
4. **日志保留**：合理设置日志保留天数，避免占用过多存储空间
5. **权限控制**：确保用户有相应的任务管理权限

## 📊 字典配置示例

### job_status（任务状态）
```json
[
  { "nameEn": "run", "nameCn": "运行中" },
  { "nameEn": "stop", "nameCn": "已停止" }
]
```

### jobGroups（任务组）
```json
[
  { "nameEn": "default", "nameCn": "默认组" },
  { "nameEn": "system", "nameCn": "系统组" },
  { "nameEn": "business", "nameCn": "业务组" }
]
```

## 🔄 后续扩展

- 任务执行日志查看
- 任务执行统计报表
- 任务依赖关系配置
- 任务执行结果通知
- 批量操作功能
