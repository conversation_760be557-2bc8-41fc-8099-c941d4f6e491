# 住户房产状态字典集成

## 📋 更新概述

根据用户需求，为住户房产新增功能集成人房状态字典查询，并设置默认状态为正常(normal)。

## ✅ 实现内容

### 🔧 1. 字典API集成

#### 导入字典API
```javascript
import { listDictByNameEn } from '@/api/system/dict'
```

#### 字典数据初始化
```javascript
async initDictData() {
  try {
    const res = await listDictByNameEn('resident_status')
    this.residentStatusList = res.data.data || []
  } catch (err) {
    console.error('加载住户状态字典失败:', err)
    // 如果字典加载失败，使用默认状态
    this.residentStatusList = [
      { nameEn: 'normal', nameCn: '正常' },
      { nameEn: 'pending', nameCn: '待审核' },
      { nameEn: 'move_out', nameCn: '搬出' }
    ]
  }
}
```

### 🔧 2. 状态选择组件更新

#### 模板更新
```vue
<!-- 更新前 -->
<el-select v-model="propertyModel.status">
  <el-option
    v-for="status in PROPERTY_STATUS"
    :key="status.value"
    :label="status.label"
    :value="status.value"
  />
</el-select>

<!-- 更新后 -->
<el-select v-model="propertyModel.status">
  <el-option
    v-for="status in residentStatusList"
    :key="status.nameEn"
    :label="status.nameCn"
    :value="status.nameEn"
  />
</el-select>
```

#### 数据结构
```javascript
data() {
  return {
    // 新增字典数据
    residentStatusList: [],
    
    // 默认状态设置为normal
    propertyModel: {
      status: 'normal',
      // 其他字段...
    }
  }
}
```

### 🔧 3. 字典数据结构

#### resident_status字典
```json
[
  {
    "id": "164",
    "nameEn": "pending",
    "nameCn": "待审核",
    "cssClass": "#E6A23C"
  },
  {
    "id": "166", 
    "nameEn": "move_out",
    "nameCn": "搬出",
    "cssClass": "#67C23A"
  },
  {
    "id": "167",
    "nameEn": "normal", 
    "nameCn": "正常",
    "cssClass": "#409EFF"
  }
]
```

### 🔧 4. 初始化流程

#### 打开对话框时初始化
```javascript
open(residentInfo, property = null) {
  this.residentInfo = { ...residentInfo }

  // 初始化字典数据
  this.initDictData()

  if (property) {
    // 编辑模式
    this.propertyModel = { ...property }
    this.dialog.title = '编辑房产信息'
  } else {
    // 新增模式
    this.resetForm()
    this.propertyModel.residentId = residentInfo.id
    this.propertyModel.communityId = getSelectedCommunityId()
    this.dialog.title = '新增房产信息'
  }

  this.dialog.show = true
  this.loadBuildingList()
}
```

#### 重置表单保持默认状态
```javascript
resetForm() {
  this.$refs.form && this.$refs.form.resetFields()
  this.propertyModel = {
    id: null,
    residentId: null,
    communityId: null,
    buildingId: null,
    roomId: null,
    address: '',
    residentType: 'owner',
    relationshipType: 'self',
    startDate: '',
    endDate: '',
    status: 'normal', // 默认状态为正常
    note: ''
  }
  this.roomList = []
}
```

## 📊 功能特点

### ✅ 字典驱动
- **动态加载**：从后端字典表获取状态选项
- **实时更新**：字典数据变更时前端自动同步
- **多语言支持**：nameEn存储，nameCn显示

### ✅ 默认值设置
- **新增默认**：新增房产时状态默认为"正常"(normal)
- **编辑保持**：编辑时保持原有状态值
- **重置恢复**：表单重置时恢复默认状态

### ✅ 错误处理
- **降级机制**：字典加载失败时使用默认选项
- **用户友好**：错误时不影响正常使用
- **日志记录**：记录错误信息便于调试

### ✅ 数据一致性
- **存储格式**：使用nameEn值存储到数据库
- **显示格式**：界面显示nameCn中文名称
- **验证规则**：必填字段验证确保数据完整

## 🎯 使用场景

### 新增房产
1. 打开新增房产对话框
2. 自动加载resident_status字典
3. 状态字段默认选中"正常"
4. 用户可根据需要修改状态
5. 提交时保存nameEn值到数据库

### 编辑房产
1. 打开编辑房产对话框
2. 加载字典数据填充选项
3. 回显当前房产状态
4. 用户可修改状态
5. 提交时更新状态值

### 状态选项
- **正常(normal)**：房产关系正常有效
- **待审核(pending)**：新增房产等待审核
- **搬出(move_out)**：住户已搬出该房产

## 🔧 技术实现

### API调用
```javascript
// 获取字典数据
listDictByNameEn('resident_status')

// 数据结构
{
  code: 0,
  data: {
    data: [
      { nameEn: 'normal', nameCn: '正常', cssClass: '#409EFF' },
      { nameEn: 'pending', nameCn: '待审核', cssClass: '#E6A23C' },
      { nameEn: 'move_out', nameCn: '搬出', cssClass: '#67C23A' }
    ]
  }
}
```

### 数据绑定
```vue
<el-select v-model="propertyModel.status">
  <el-option
    v-for="status in residentStatusList"
    :key="status.nameEn"
    :label="status.nameCn"
    :value="status.nameEn"
  />
</el-select>
```

## ✅ 完成状态

- ✅ **字典API集成** - 完成
- ✅ **状态选择更新** - 完成  
- ✅ **默认值设置** - 完成
- ✅ **错误处理机制** - 完成
- ✅ **数据一致性保证** - 完成

住户房产状态功能已成功集成字典管理，新增时默认状态为正常！🎉
